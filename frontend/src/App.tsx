import React, { useState, useEffect } from 'react'
import { Toaster } from 'react-hot-toast'
import { motion, AnimatePresence } from 'framer-motion'
import {
  BarChart3,
  Search,
  Bot,
  Database,
  Moon,
  Sun,
  Sparkles,
  TrendingUp
} from 'lucide-react'
import AnalysisForm from './components/AnalysisForm'
import TaskProgress from './components/TaskProgress'
import ResultsDisplay from './components/ResultsDisplay'
import ImportData from './components/ImportData'

function App() {
  const [activeTab, setActiveTab] = useState('analysis')
  const [currentTask, setCurrentTask] = useState<string | null>(null)
  const [analysisResults, setAnalysisResults] = useState<any>(null)
  const [isDark, setIsDark] = useState(false)

  const tabs = [
    {
      id: 'analysis',
      label: '年报分析',
      icon: BarChart3,
      description: '在线下载并分析年报数据'
    },
    {
      id: 'keyword',
      label: '关键词分析',
      icon: Search,
      description: '基于本地数据库的关键词统计'
    },
    {
      id: 'ai',
      label: 'AI分析',
      icon: Bot,
      description: '智能AI深度分析年报内容'
    },
    {
      id: 'import',
      label: '数据导入',
      icon: Database,
      description: '导入和管理本地数据'
    },
  ]

  // 主题切换
  useEffect(() => {
    if (isDark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [isDark])

  const handleTaskStart = (taskId: string) => {
    setCurrentTask(taskId)
    setAnalysisResults(null)
  }

  const handleTaskComplete = (results: any) => {
    setCurrentTask(null)
    setAnalysisResults(results)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 transition-colors duration-300">
      {/* 背景装饰 */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      {/* Header */}
      <motion.header
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-b border-gray-200 dark:border-gray-800"
      >
        <div className="container mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <motion.div
              className="flex items-center space-x-4"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <div className="relative">
                <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-primary-500 to-purple-600 rounded-xl shadow-lg">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-success-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-2 h-2 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                  巨潮资讯网年报分析平台
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                  智能年报分析与关键词统计 · 数据驱动决策
                </p>
              </div>
            </motion.div>

            <div className="flex items-center space-x-4">
              <motion.button
                onClick={() => setIsDark(!isDark)}
                className="btn-ghost btn-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {isDark ? (
                  <Sun className="w-4 h-4" />
                ) : (
                  <Moon className="w-4 h-4" />
                )}
              </motion.button>

              <div className="hidden sm:flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
                <span>系统运行正常</span>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="relative container mx-auto px-6 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Tabs */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="mb-8"
          >
            <div className="flex flex-wrap gap-2 p-1 bg-gray-100 dark:bg-gray-800 rounded-xl">
              {tabs.map((tab) => {
                const Icon = tab.icon
                const isActive = activeTab === tab.id

                return (
                  <motion.button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`relative flex items-center space-x-3 px-6 py-3 rounded-lg font-medium text-sm transition-all duration-200 ${
                      isActive
                        ? 'bg-white dark:bg-gray-700 text-primary-600 dark:text-primary-400 shadow-soft'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-white/50 dark:hover:bg-gray-700/50'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    layout
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                    {isActive && (
                      <motion.div
                        layoutId="activeTab"
                        className="absolute inset-0 bg-white dark:bg-gray-700 rounded-lg shadow-soft -z-10"
                        transition={{ type: "spring", stiffness: 500, damping: 30 }}
                      />
                    )}
                  </motion.button>
                )
              })}
            </div>

            {/* Tab Description */}
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                className="mt-4 text-center"
              >
                <p className="text-gray-600 dark:text-gray-400">
                  {tabs.find(tab => tab.id === activeTab)?.description}
                </p>
              </motion.div>
            </AnimatePresence>
          </motion.div>

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {currentTask && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="mb-8"
                >
                  <TaskProgress
                    taskId={currentTask}
                    onComplete={handleTaskComplete}
                  />
                </motion.div>
              )}

              {activeTab === 'analysis' && (
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <AnalysisForm
                      mode="online"
                      onTaskStart={handleTaskStart}
                    />
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    {analysisResults ? (
                      <ResultsDisplay results={analysisResults} />
                    ) : (
                      <div className="card h-full flex items-center justify-center">
                        <div className="text-center">
                          <BarChart3 className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                          <p className="text-gray-500 dark:text-gray-400">
                            分析结果将在这里显示
                          </p>
                        </div>
                      </div>
                    )}
                  </motion.div>
                </div>
              )}

              {activeTab === 'keyword' && (
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <AnalysisForm
                      mode="local"
                      onTaskStart={handleTaskStart}
                    />
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    {analysisResults ? (
                      <ResultsDisplay results={analysisResults} />
                    ) : (
                      <div className="card h-full flex items-center justify-center">
                        <div className="text-center">
                          <Search className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                          <p className="text-gray-500 dark:text-gray-400">
                            关键词分析结果将在这里显示
                          </p>
                        </div>
                      </div>
                    )}
                  </motion.div>
                </div>
              )}

              {activeTab === 'ai' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="space-y-6"
                >
                  <div className="card-gradient">
                    <div className="flex items-center space-x-4 mb-6">
                      <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl">
                        <Bot className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">AI智能分析</h2>
                        <p className="text-gray-600 dark:text-gray-400">
                          使用AI技术对年报内容进行深度分析，提供智能洞察和总结
                        </p>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6">
                      <div className="flex items-start space-x-3">
                        <Sparkles className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                        <div>
                          <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                            配置提示
                          </h3>
                          <p className="text-sm text-yellow-700 dark:text-yellow-300">
                            AI分析功能需要配置OpenAI API密钥，请在后端.env文件中设置相关参数。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {activeTab === 'import' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <ImportData />
                </motion.div>
              )}
            </motion.div>
          </AnimatePresence>
        </div>
      </main>

      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          className: 'dark:bg-gray-800 dark:text-white',
          style: {
            background: 'var(--toast-bg, #363636)',
            color: 'var(--toast-color, #fff)',
            borderRadius: '12px',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
          },
          success: {
            iconTheme: {
              primary: '#22c55e',
              secondary: '#fff',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
        }}
      />
    </div>
  )
}

export default App
