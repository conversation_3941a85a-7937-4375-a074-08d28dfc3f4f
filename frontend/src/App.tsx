import React, { useState, useEffect } from 'react'
import { Toaster } from 'react-hot-toast'
import { motion, AnimatePresence } from 'framer-motion'
import {
  BarChart3,
  Search,
  Bot,
  Database,
  Moon,
  Sun,
  Home,
  FileText,
  MessageSquare,
  Settings
} from 'lucide-react'
import AnalysisForm from './components/AnalysisForm'
import TaskProgress from './components/TaskProgress'
import ResultsDisplay from './components/ResultsDisplay'
import ImportData from './components/ImportData'
import AIAnalysis from './components/AIAnalysis'

function App() {
  const [activeTab, setActiveTab] = useState('analysis')
  const [currentTask, setCurrentTask] = useState<string | null>(null)
  const [analysisResults, setAnalysisResults] = useState<any>(null)
  const [currentAnalysisId, setCurrentAnalysisId] = useState<string | null>(null)
  const [isDark, setIsDark] = useState(false)
  const [isFormCollapsed, setIsFormCollapsed] = useState(false)

  const tabs = [
    {
      id: 'analysis',
      label: '年报分析',
      icon: BarChart3,
      description: '在线下载并分析年报数据'
    },
    {
      id: 'keyword',
      label: '关键词分析',
      icon: Search,
      description: '基于本地数据库的关键词统计'
    },
    {
      id: 'ai',
      label: 'AI分析',
      icon: Bot,
      description: '智能AI深度分析年报内容'
    },
    {
      id: 'import',
      label: '数据导入',
      icon: Database,
      description: '导入和管理本地数据'
    },
  ]

  // 主题切换
  useEffect(() => {
    if (isDark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [isDark])

  const handleTaskStart = (taskId: string) => {
    setCurrentTask(taskId)
    setAnalysisResults(null)
  }

  const handleTaskComplete = (results: any) => {
    setCurrentTask(null)
    setAnalysisResults(results)
  }

  const handleResultsReady = (results: any, analysisId?: string) => {
    setAnalysisResults(results)
    setCurrentAnalysisId(analysisId || null)
    // 共享分析结果到两个模式
    console.log('📊 分析结果已更新，两个模式都可以查看')
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 transition-colors duration-300">
      {/* 背景装饰 */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      {/* Header */}
      <motion.header
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-b border-gray-200 dark:border-gray-800"
      >
        <div className="container mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <motion.div
              className="flex items-center space-x-4"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <div className="flex items-center justify-center w-12 h-12 bg-black dark:bg-white rounded-xl shadow-lg">
                <BarChart3 className="w-6 h-6 text-white dark:text-black" />
              </div>
              <div>
                <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                  巨潮资讯网年报分析平台
                </h1>
                <p className="text-xs text-gray-600 dark:text-gray-400 font-medium">
                  智能年报分析与关键词统计 · 数据驱动决策
                </p>
              </div>
            </motion.div>

            <div className="flex items-center space-x-4">
              <motion.button
                onClick={() => setIsDark(!isDark)}
                className="btn-ghost btn-sm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {isDark ? (
                  <Sun className="w-4 h-4" />
                ) : (
                  <Moon className="w-4 h-4" />
                )}
              </motion.button>

              <div className="hidden sm:flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>在线</span>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="relative container mx-auto px-6 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Tabs */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="mb-8"
          >
            <div className="flex justify-center">
              <div className="inline-flex items-center gap-1 p-1 bg-gray-100/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full border border-gray-200/50 dark:border-gray-700/50 shadow-lg">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  const isActive = activeTab === tab.id

                  return (
                    <motion.button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`relative flex items-center space-x-2 px-4 py-2 rounded-full font-medium text-sm transition-all duration-200 ${
                        isActive
                          ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                          : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-white/50 dark:hover:bg-gray-700/50'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      layout
                    >
                      <Icon className="w-4 h-4" />
                      <span className="hidden sm:inline">{tab.label}</span>
                      {isActive && (
                        <motion.div
                          layoutId="activeTab"
                          className="absolute inset-0 bg-white dark:bg-gray-700 rounded-full shadow-sm -z-10"
                          transition={{ type: "spring", stiffness: 500, damping: 30 }}
                        />
                      )}
                    </motion.button>
                  )
                })}
              </div>
            </div>

            {/* Tab Description */}
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                className="mt-4 text-center"
              >
                <p className="text-gray-600 dark:text-gray-400">
                  {tabs.find(tab => tab.id === activeTab)?.description}
                </p>
              </motion.div>
            </AnimatePresence>
          </motion.div>

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {currentTask && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="mb-8"
                >
                  <TaskProgress
                    taskId={currentTask}
                    onComplete={handleTaskComplete}
                  />
                </motion.div>
              )}

              {activeTab === 'analysis' && (
                <div className="flex gap-8">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                    className={`transition-all duration-300 ${isFormCollapsed ? 'w-20' : 'w-full xl:w-1/2'}`}
                  >
                    <AnalysisForm
                      mode="online"
                      onTaskStart={handleTaskStart}
                      onResultsReady={handleResultsReady}
                      isCollapsed={isFormCollapsed}
                      onToggleCollapse={() => setIsFormCollapsed(!isFormCollapsed)}
                    />
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                    className={`transition-all duration-300 ${isFormCollapsed ? 'flex-1' : 'w-full xl:w-1/2'}`}
                  >
                    {analysisResults ? (
                      <ResultsDisplay results={analysisResults} analysisId={currentAnalysisId} />
                    ) : (
                      <div className="card h-full flex items-center justify-center">
                        <div className="text-center">
                          <BarChart3 className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                          <p className="text-gray-500 dark:text-gray-400">
                            分析结果将在这里显示
                          </p>
                        </div>
                      </div>
                    )}
                  </motion.div>
                </div>
              )}

              {activeTab === 'keyword' && (
                <div className="flex gap-8">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                    className={`transition-all duration-300 ${isFormCollapsed ? 'w-20' : 'w-full xl:w-1/2'}`}
                  >
                    <AnalysisForm
                      mode="local"
                      onTaskStart={handleTaskStart}
                      onResultsReady={handleResultsReady}
                      isCollapsed={isFormCollapsed}
                      onToggleCollapse={() => setIsFormCollapsed(!isFormCollapsed)}
                    />
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                    className={`transition-all duration-300 ${isFormCollapsed ? 'flex-1' : 'w-full xl:w-1/2'}`}
                  >
                    {analysisResults ? (
                      <ResultsDisplay results={analysisResults} analysisId={currentAnalysisId} />
                    ) : (
                      <div className="card h-full flex items-center justify-center">
                        <div className="text-center">
                          <Search className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                          <p className="text-gray-500 dark:text-gray-400">
                            关键词分析结果将在这里显示
                          </p>
                        </div>
                      </div>
                    )}
                  </motion.div>
                </div>
              )}

              {activeTab === 'ai' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <AIAnalysis />
                </motion.div>
              )}

              {activeTab === 'import' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <ImportData />
                </motion.div>
              )}
            </motion.div>
          </AnimatePresence>
        </div>
      </main>

      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          className: 'dark:bg-gray-800 dark:text-white',
          style: {
            background: 'var(--toast-bg, #363636)',
            color: 'var(--toast-color, #fff)',
            borderRadius: '12px',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
          },
          success: {
            iconTheme: {
              primary: '#22c55e',
              secondary: '#fff',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
        }}
      />
    </div>
  )
}

export default App
