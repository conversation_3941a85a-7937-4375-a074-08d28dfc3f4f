@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200 dark:border-gray-800;
  }

  html {
    font-family: 'Inter', system-ui, sans-serif;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }

  body {
    @apply bg-gray-50 text-gray-900 antialiased;
    @apply dark:bg-gray-950 dark:text-gray-50;
  }

  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
}

@layer components {
  /* 按钮组件 */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn bg-primary-600 hover:bg-primary-700 text-white shadow-sm hover:shadow-md focus:ring-primary-500;
    @apply dark:bg-primary-500 dark:hover:bg-primary-600;
  }

  .btn-secondary {
    @apply btn bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 shadow-sm hover:shadow-md focus:ring-gray-500;
    @apply dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-300 dark:border-gray-600;
  }

  .btn-danger {
    @apply btn bg-error-600 hover:bg-error-700 text-white shadow-sm hover:shadow-md focus:ring-error-500;
  }

  .btn-ghost {
    @apply btn bg-transparent hover:bg-gray-100 text-gray-700 focus:ring-gray-500;
    @apply dark:hover:bg-gray-800 dark:text-gray-300;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-md {
    @apply px-4 py-2 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-sm;
  }

  /* 输入框组件 */
  .input-field {
    @apply w-full px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm text-gray-900 placeholder-gray-500 transition-all duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
    @apply dark:bg-gray-800 dark:border-gray-700 dark:text-gray-100 dark:placeholder-gray-400;
  }

  .textarea-field {
    @apply input-field resize-none min-h-[120px];
  }

  /* 卡片组件 */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-4;
  }

  .card-gradient {
    @apply bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 p-4;
  }

  /* 卡片组件 */
  .card {
    @apply bg-white rounded-xl border border-gray-200 shadow-soft p-6 transition-all duration-200;
    @apply dark:bg-gray-900 dark:border-gray-800;
  }

  .card-hover {
    @apply card hover:shadow-medium hover:border-gray-300;
    @apply dark:hover:border-gray-700;
  }

  .card-gradient {
    @apply relative overflow-hidden;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
  }

  /* 表格组件 */
  .table-container {
    @apply overflow-hidden bg-white rounded-xl border border-gray-200 shadow-soft;
    @apply dark:bg-gray-900 dark:border-gray-800;
  }

  .table-header {
    @apply bg-gray-50 px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider;
    @apply dark:bg-gray-800 dark:text-gray-400;
  }

  .table-cell {
    @apply px-4 py-3 text-xs text-gray-900 border-t border-gray-100;
    @apply dark:text-gray-100 dark:border-gray-800;
  }

  .table-row {
    @apply transition-colors duration-150 hover:bg-gray-50;
    @apply dark:hover:bg-gray-800;
  }

  /* 标签组件 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
    @apply dark:bg-primary-900 dark:text-primary-200;
  }

  .badge-success {
    @apply badge bg-success-100 text-success-800;
    @apply dark:bg-success-900 dark:text-success-200;
  }

  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
    @apply dark:bg-warning-900 dark:text-warning-200;
  }

  .badge-error {
    @apply badge bg-error-100 text-error-800;
    @apply dark:bg-error-900 dark:text-error-200;
  }

  /* 加载动画 */
  .loading-shimmer {
    @apply relative overflow-hidden bg-gray-200 rounded;
    @apply dark:bg-gray-700;
  }

  .loading-shimmer::after {
    @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white to-transparent opacity-60;
    @apply dark:via-gray-600;
    content: '';
    animation: shimmer 2s infinite;
  }

  /* 渐变背景 */
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-mesh {
    background: radial-gradient(circle at 25% 25%, #667eea 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #764ba2 0%, transparent 50%),
                linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }
}
