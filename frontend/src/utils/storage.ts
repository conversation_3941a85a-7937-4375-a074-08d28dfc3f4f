// localStorage 工具函数

export interface FormCache {
  stockCodes: string
  keywords: string
  searchKeyword: string
  startDate: string
  endDate: string
  relatedParties: string
  timestamp: number
}

const CACHE_KEY = 'cninfo_form_cache'
const CACHE_EXPIRY = 24 * 60 * 60 * 1000 // 24小时

export const saveFormCache = (data: Omit<FormCache, 'timestamp'>) => {
  try {
    const cacheData: FormCache = {
      ...data,
      timestamp: Date.now()
    }
    localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData))
  } catch (error) {
    console.warn('保存表单缓存失败:', error)
  }
}

export const loadFormCache = (): Partial<FormCache> | null => {
  try {
    const cached = localStorage.getItem(CACHE_KEY)
    if (!cached) return null

    const data: FormCache = JSON.parse(cached)
    
    // 检查缓存是否过期
    if (Date.now() - data.timestamp > CACHE_EXPIRY) {
      localStorage.removeItem(CACHE_KEY)
      return null
    }

    return data
  } catch (error) {
    console.warn('加载表单缓存失败:', error)
    return null
  }
}

export const clearFormCache = () => {
  try {
    localStorage.removeItem(CACHE_KEY)
  } catch (error) {
    console.warn('清除表单缓存失败:', error)
  }
}
