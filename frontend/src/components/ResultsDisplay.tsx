import React, { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Filter, 
  Download, 
  ChevronUp, 
  ChevronDown, 
  Eye, 
  BarChart3,
  Search,
  Building2,
  Hash,
  TrendingUp,
  FileText,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

interface ResultsDisplayProps {
  results: any
}

interface FlattenedResult {
  stock_code: string
  company_name: string
  file_name: string
  keyword: string
  count: number
}

const ResultsDisplay: React.FC<ResultsDisplayProps> = ({ results }) => {
  const [filters, setFilters] = useState({
    keyword: '',
    company: '',
    minCount: 0,
    hideZero: false,
  })
  const [sortBy, setSortBy] = useState<'count' | 'company' | 'keyword'>('count')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const [showFilters, setShowFilters] = useState(false)

  // 转换数据格式
  const flattenedResults = useMemo((): FlattenedResult[] => {
    console.log('🔍 处理分析结果数据:', results)
    
    if (!results) {
      return []
    }
    
    const flattened: FlattenedResult[] = []
    
    // 处理不同的数据格式
    if (Array.isArray(results)) {
      // 如果results已经是扁平化的数组
      return results.map(item => ({
        stock_code: item.stock_code || item.stockCode || '',
        company_name: item.company_name || item.companyName || item.stock_code || item.stockCode || '',
        file_name: item.file_name || item.fileName || '',
        keyword: item.keyword || '',
        count: item.count || 0
      }))
    }
    
    // 处理嵌套对象格式 - 后端返回的标准格式
    if (typeof results === 'object') {
      // 检查是否有analysis_results字段
      const analysisData = results.analysis_results || results
      
      Object.entries(analysisData || {}).forEach(([stockCode, companyData]: [string, any]) => {
        if (typeof companyData === 'object' && companyData !== null) {
          Object.entries(companyData).forEach(([fileName, keywordData]: [string, any]) => {
            if (typeof keywordData === 'object' && keywordData !== null) {
              Object.entries(keywordData).forEach(([keyword, count]: [string, any]) => {
                flattened.push({
                  stock_code: stockCode,
                  company_name: stockCode, // 可以从其他地方获取公司名称
                  file_name: fileName,
                  keyword: keyword,
                  count: typeof count === 'number' ? count : parseInt(count) || 0
                })
              })
            }
          })
        }
      })
    }
    
    console.log('📊 扁平化结果:', flattened)
    return flattened
  }, [results])

  // 过滤和排序数据
  const filteredResults = flattenedResults
    .filter(item => {
      if (filters.keyword && !item.keyword.toLowerCase().includes(filters.keyword.toLowerCase())) {
        return false
      }
      if (filters.company && !item.company_name.toLowerCase().includes(filters.company.toLowerCase())) {
        return false
      }
      if (filters.minCount > 0 && item.count < filters.minCount) {
        return false
      }
      if (filters.hideZero && item.count === 0) {
        return false
      }
      return true
    })
    .sort((a, b) => {
      let aVal, bVal
      switch (sortBy) {
        case 'count':
          aVal = a.count
          bVal = b.count
          break
        case 'company':
          aVal = a.company_name
          bVal = b.company_name
          break
        case 'keyword':
          aVal = a.keyword
          bVal = b.keyword
          break
        default:
          return 0
      }
      
      if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase()
        bVal = bVal.toLowerCase()
      }
      
      if (sortOrder === 'asc') {
        return aVal > bVal ? 1 : -1
      } else {
        return aVal < bVal ? 1 : -1
      }
    })

  // 分页
  const totalPages = Math.ceil(filteredResults.length / pageSize)
  const paginatedResults = filteredResults.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  )

  const handleSort = (field: 'count' | 'company' | 'keyword') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('desc')
    }
  }

  // 统计信息
  const stats = useMemo(() => {
    const totalKeywords = new Set(flattenedResults.map(r => r.keyword)).size
    const totalCompanies = new Set(flattenedResults.map(r => r.stock_code)).size
    const totalOccurrences = flattenedResults.reduce((sum, r) => sum + r.count, 0)
    const nonZeroResults = flattenedResults.filter(r => r.count > 0).length
    
    return {
      totalKeywords,
      totalCompanies,
      totalOccurrences,
      nonZeroResults,
      totalResults: flattenedResults.length
    }
  }, [flattenedResults])

  const handleExport = () => {
    // 导出功能实现
    console.log('导出数据:', filteredResults)
    // TODO: 实现导出功能
  }

  const SortIcon = ({ field }: { field: string }) => {
    if (sortBy !== field) return null
    return sortOrder === 'asc' ? (
      <ChevronUp className="w-4 h-4" />
    ) : (
      <ChevronDown className="w-4 h-4" />
    )
  }

  if (!flattenedResults.length) {
    return (
      <motion.div 
        className="card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="text-center py-12">
          <BarChart3 className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            暂无分析结果
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            请先执行分析任务以查看结果
          </p>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div 
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      {/* 统计卡片 */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div 
          className="card bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-800"
          whileHover={{ scale: 1.02 }}
        >
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-500 rounded-lg">
              <Building2 className="w-5 h-5 text-white" />
            </div>
            <div>
              <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">公司数量</p>
              <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{stats.totalCompanies}</p>
            </div>
          </div>
        </motion.div>

        <motion.div 
          className="card bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-800"
          whileHover={{ scale: 1.02 }}
        >
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-500 rounded-lg">
              <Hash className="w-5 h-5 text-white" />
            </div>
            <div>
              <p className="text-sm text-green-600 dark:text-green-400 font-medium">关键词数量</p>
              <p className="text-2xl font-bold text-green-900 dark:text-green-100">{stats.totalKeywords}</p>
            </div>
          </div>
        </motion.div>

        <motion.div 
          className="card bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-purple-200 dark:border-purple-800"
          whileHover={{ scale: 1.02 }}
        >
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-500 rounded-lg">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div>
              <p className="text-sm text-purple-600 dark:text-purple-400 font-medium">总出现次数</p>
              <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">{stats.totalOccurrences}</p>
            </div>
          </div>
        </motion.div>

        <motion.div 
          className="card bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-orange-200 dark:border-orange-800"
          whileHover={{ scale: 1.02 }}
        >
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-500 rounded-lg">
              <FileText className="w-5 h-5 text-white" />
            </div>
            <div>
              <p className="text-sm text-orange-600 dark:text-orange-400 font-medium">有效结果</p>
              <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">{stats.nonZeroResults}</p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* 主要结果表格 */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">分析结果</h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              共 {filteredResults.length} 条结果
              {filteredResults.length !== flattenedResults.length && (
                <span className="ml-2">（已筛选，原始数据 {flattenedResults.length} 条）</span>
              )}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <motion.button
              onClick={() => setShowFilters(!showFilters)}
              className={`btn-secondary btn-md flex items-center space-x-2 ${showFilters ? 'bg-primary-50 text-primary-700 border-primary-200' : ''}`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Filter className="w-4 h-4" />
              <span>筛选</span>
            </motion.button>
            <motion.button
              onClick={handleExport}
              className="btn-primary btn-md flex items-center space-x-2"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Download className="w-4 h-4" />
              <span>导出</span>
            </motion.button>
          </div>
        </div>

        {/* 筛选器 */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6 mb-6 border border-gray-200 dark:border-gray-700"
            >
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <Search className="w-4 h-4" />
                    <span>关键词</span>
                  </label>
                  <input
                    type="text"
                    className="input-field"
                    placeholder="搜索关键词"
                    value={filters.keyword}
                    onChange={(e) => setFilters({ ...filters, keyword: e.target.value })}
                  />
                </div>
                <div>
                  <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <Building2 className="w-4 h-4" />
                    <span>公司名称</span>
                  </label>
                  <input
                    type="text"
                    className="input-field"
                    placeholder="搜索公司"
                    value={filters.company}
                    onChange={(e) => setFilters({ ...filters, company: e.target.value })}
                  />
                </div>
                <div>
                  <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <Hash className="w-4 h-4" />
                    <span>最小出现次数</span>
                  </label>
                  <input
                    type="number"
                    className="input-field"
                    min="0"
                    placeholder="0"
                    value={filters.minCount}
                    onChange={(e) => setFilters({ ...filters, minCount: parseInt(e.target.value) || 0 })}
                  />
                </div>
                <div className="flex items-end">
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.hideZero}
                      onChange={(e) => setFilters({ ...filters, hideZero: e.target.checked })}
                      className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">隐藏零次记录</span>
                  </label>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 数据表格 */}
        <div className="table-container">
          <table className="min-w-full">
            <thead>
              <tr>
                <th
                  className="table-header cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  onClick={() => handleSort('company')}
                >
                  <div className="flex items-center space-x-2">
                    <Building2 className="w-4 h-4" />
                    <span>公司名称</span>
                    <SortIcon field="company" />
                  </div>
                </th>
                <th
                  className="table-header cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  onClick={() => handleSort('keyword')}
                >
                  <div className="flex items-center space-x-2">
                    <Hash className="w-4 h-4" />
                    <span>关键词</span>
                    <SortIcon field="keyword" />
                  </div>
                </th>
                <th
                  className="table-header cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  onClick={() => handleSort('count')}
                >
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4" />
                    <span>出现次数</span>
                    <SortIcon field="count" />
                  </div>
                </th>
                <th className="table-header">
                  <div className="flex items-center space-x-2">
                    <Eye className="w-4 h-4" />
                    <span>操作</span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <AnimatePresence>
                {paginatedResults.map((item, index) => (
                  <motion.tr
                    key={`${item.stock_code}-${item.keyword}-${index}`}
                    className="table-row"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ delay: index * 0.02 }}
                  >
                    <td className="table-cell">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-sm font-bold">
                          {item.company_name.slice(0, 1)}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            {item.company_name}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {item.stock_code}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <span className="badge-primary">
                        {item.keyword}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className={`badge ${
                        item.count > 0
                          ? 'badge-success'
                          : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                      }`}>
                        {item.count}
                      </span>
                    </td>
                    <td className="table-cell">
                      {item.count > 0 && (
                        <motion.button
                          className="btn-ghost btn-sm flex items-center space-x-2 text-primary-600 hover:text-primary-700"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Eye className="w-4 h-4" />
                          <span>查看上下文</span>
                        </motion.button>
                      )}
                    </td>
                  </motion.tr>
                ))}
              </AnimatePresence>
            </tbody>
          </table>
        </div>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              显示第 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, filteredResults.length)} 条，
              共 {filteredResults.length} 条
            </div>
            <div className="flex items-center space-x-2">
              <motion.button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                whileHover={{ scale: currentPage === 1 ? 1 : 1.02 }}
                whileTap={{ scale: currentPage === 1 ? 1 : 0.98 }}
              >
                <ChevronLeft className="w-4 h-4" />
                <span>上一页</span>
              </motion.button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum
                  if (totalPages <= 5) {
                    pageNum = i + 1
                  } else if (currentPage <= 3) {
                    pageNum = i + 1
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i
                  } else {
                    pageNum = currentPage - 2 + i
                  }

                  return (
                    <motion.button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`w-8 h-8 rounded-lg text-sm font-medium transition-colors ${
                        currentPage === pageNum
                          ? 'bg-primary-600 text-white'
                          : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      {pageNum}
                    </motion.button>
                  )
                })}
              </div>

              <motion.button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                whileHover={{ scale: currentPage === totalPages ? 1 : 1.02 }}
                whileTap={{ scale: currentPage === totalPages ? 1 : 0.98 }}
              >
                <span>下一页</span>
                <ChevronRight className="w-4 h-4" />
              </motion.button>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  )
}

export default ResultsDisplay
