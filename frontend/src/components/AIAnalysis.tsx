import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import axios from 'axios'
import { motion } from 'framer-motion'
import { 
  Play, 
  Loader2, 
  Building2, 
  Hash, 
  Users,
  MessageSquare,
  Settings,
  TestTube,
  Bot,
  Sparkles
} from 'lucide-react'
import { saveFormCache, loadFormCache } from '../utils/storage'

const AIAnalysis: React.FC = () => {
  const [formData, setFormData] = useState({
    stockCodes: '',
    keywords: '',
    relatedParties: '',
    prompt: '',
    openaiConfig: {
      useDefault: true,
      apiKey: '',
      baseUrl: '',
      model: 'gpt-3.5-turbo'
    }
  })
  const [isLoading, setIsLoading] = useState(false)
  const [isTestingOpenAI, setIsTestingOpenAI] = useState(false)
  const [focusedField, setFocusedField] = useState<string | null>(null)
  const [analysisResult, setAnalysisResult] = useState<string>('')
  const [showConfig, setShowConfig] = useState(false)

  // 加载缓存数据
  useEffect(() => {
    const cached = loadFormCache()
    if (cached) {
      setFormData(prev => ({
        ...prev,
        stockCodes: cached.stockCodes || prev.stockCodes,
        keywords: cached.keywords || prev.keywords,
        relatedParties: cached.relatedParties || prev.relatedParties,
      }))
    }
  }, [])

  // 保存缓存数据
  useEffect(() => {
    const timer = setTimeout(() => {
      if (formData.stockCodes || formData.keywords) {
        saveFormCache({
          stockCodes: formData.stockCodes,
          keywords: formData.keywords,
          searchKeyword: '',
          startDate: '',
          endDate: '',
          relatedParties: formData.relatedParties
        })
      }
    }, 1000)

    return () => clearTimeout(timer)
  }, [formData])

  const testOpenAI = async () => {
    setIsTestingOpenAI(true)
    try {
      const response = await axios.post('/api/test_openai', formData.openaiConfig)
      if (response.data.success) {
        toast.success('OpenAI连接测试成功')
      } else {
        toast.error(response.data.message || 'OpenAI连接测试失败')
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'OpenAI连接测试失败')
    } finally {
      setIsTestingOpenAI(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.stockCodes.trim()) {
      toast.error('请输入股票代码')
      return
    }
    
    if (!formData.keywords.trim()) {
      toast.error('请输入分析关键词')
      return
    }
    
    if (!formData.relatedParties.trim()) {
      toast.error('请输入关联方')
      return
    }
    
    if (!formData.prompt.trim()) {
      toast.error('请输入AI分析要求')
      return
    }

    setIsLoading(true)
    setAnalysisResult('')
    
    try {
      const payload = {
        stock_codes: formData.stockCodes,
        keywords: formData.keywords,
        related_parties: formData.relatedParties,
        prompt: formData.prompt,
        openai_config: formData.openaiConfig
      }

      const response = await axios.post('/api/ai_analysis', payload)
      
      if (response.data.success) {
        setAnalysisResult(response.data.analysis_result)
        toast.success('AI分析完成')
      } else {
        toast.error(response.data.message || 'AI分析失败')
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'AI分析请求失败')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* AI分析表单 */}
      <motion.div
        className="card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-black dark:bg-white rounded-lg">
            <Bot className="w-5 h-5 text-white dark:text-black" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-gray-900 dark:text-white">AI智能分析</h2>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              基于年报数据进行AI深度分析
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基础输入 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 股票代码 */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
            >
              <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                <Building2 className="w-4 h-4" />
                <span>股票代码</span>
                <span className="text-red-500">*</span>
              </label>
              <textarea
                className={`textarea-field transition-all duration-200 ${
                  focusedField === 'stockCodes' ? 'ring-2 ring-black dark:ring-white border-transparent' : ''
                }`}
                placeholder="每行一个股票代码，例如：&#10;000001&#10;000002&#10;300001"
                value={formData.stockCodes}
                onChange={(e) => setFormData({ ...formData, stockCodes: e.target.value })}
                onFocus={() => setFocusedField('stockCodes')}
                onBlur={() => setFocusedField(null)}
                required
                rows={4}
              />
            </motion.div>

            {/* 关键词 */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                <Hash className="w-4 h-4" />
                <span>分析关键词</span>
                <span className="text-red-500">*</span>
              </label>
              <textarea
                className={`textarea-field transition-all duration-200 ${
                  focusedField === 'keywords' ? 'ring-2 ring-black dark:ring-white border-transparent' : ''
                }`}
                placeholder="每行一个关键词，例如：&#10;人工智能&#10;大数据&#10;云计算"
                value={formData.keywords}
                onChange={(e) => setFormData({ ...formData, keywords: e.target.value })}
                onFocus={() => setFocusedField('keywords')}
                onBlur={() => setFocusedField(null)}
                required
                rows={4}
              />
            </motion.div>
          </div>

          {/* 关联方 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
              <Users className="w-4 h-4" />
              <span>关联方</span>
              <span className="text-red-500">*</span>
            </label>
            <textarea
              className={`textarea-field transition-all duration-200 ${
                focusedField === 'relatedParties' ? 'ring-2 ring-black dark:ring-white border-transparent' : ''
              }`}
              placeholder="每行一个关联方，例如：&#10;华为技术有限公司&#10;腾讯科技&#10;阿里巴巴"
              value={formData.relatedParties}
              onChange={(e) => setFormData({ ...formData, relatedParties: e.target.value })}
              onFocus={() => setFocusedField('relatedParties')}
              onBlur={() => setFocusedField(null)}
              required
              rows={3}
            />
          </motion.div>

          {/* AI分析要求 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
              <MessageSquare className="w-4 h-4" />
              <span>AI分析要求</span>
              <span className="text-red-500">*</span>
            </label>
            <textarea
              className={`textarea-field transition-all duration-200 ${
                focusedField === 'prompt' ? 'ring-2 ring-black dark:ring-white border-transparent' : ''
              }`}
              placeholder="请描述您希望AI分析的具体内容，例如：&#10;分析这些公司与关联方在人工智能领域的合作情况，包括合作模式、技术交流、共同研发等方面"
              value={formData.prompt}
              onChange={(e) => setFormData({ ...formData, prompt: e.target.value })}
              onFocus={() => setFocusedField('prompt')}
              onBlur={() => setFocusedField(null)}
              required
              rows={4}
            />
          </motion.div>

          {/* 提交按钮 */}
          <motion.div
            className="flex justify-end pt-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <motion.button
              type="submit"
              disabled={isLoading}
              className="btn-primary btn-lg flex items-center space-x-3 disabled:opacity-50 disabled:cursor-not-allowed bg-black hover:bg-gray-800 dark:bg-white dark:hover:bg-gray-200 dark:text-black"
              whileHover={{ scale: isLoading ? 1 : 1.02 }}
              whileTap={{ scale: isLoading ? 1 : 0.98 }}
            >
              {isLoading ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Sparkles className="w-5 h-5" />
              )}
              <span className="font-semibold">
                {isLoading ? 'AI分析中...' : '开始AI分析'}
              </span>
            </motion.button>
          </motion.div>
        </form>
      </motion.div>

      {/* 分析结果 */}
      {analysisResult && (
        <motion.div
          className="card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-black dark:bg-white rounded-lg">
              <Bot className="w-5 h-5 text-white dark:text-black" />
            </div>
            <h3 className="text-lg font-bold text-gray-900 dark:text-white">AI分析结果</h3>
          </div>
          <div className="prose prose-gray dark:prose-invert max-w-none">
            <div className="whitespace-pre-wrap text-gray-700 dark:text-gray-300 leading-relaxed">
              {analysisResult}
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default AIAnalysis
