import React, { useState } from 'react'
import toast from 'react-hot-toast'
import axios from 'axios'
import { motion } from 'framer-motion'
import {
  Database,
  Upload,
  Trash2,
  Bug,
  Loader2,
  FileText,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react'

const ImportData: React.FC = () => {
  const [txtDir, setTxtDir] = useState('txt')
  const [isImporting, setIsImporting] = useState(false)
  const [importResult, setImportResult] = useState<any>(null)

  const handleImport = async () => {
    if (!txtDir.trim()) {
      toast.error('请输入TXT文件目录')
      return
    }

    setIsImporting(true)
    setImportResult(null)

    try {
      const response = await axios.post('/api/import_txt', {
        txt_dir: txtDir
      })

      if (response.data.success) {
        setImportResult(response.data)
        toast.success(response.data.message)
      } else {
        toast.error(response.data.message || '导入失败')
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || '导入请求失败')
    } finally {
      setIsImporting(false)
    }
  }

  const handleCleanDuplicates = async () => {
    try {
      const response = await axios.post('/api/clean_duplicates')
      if (response.data.success) {
        toast.success(response.data.message)
      } else {
        toast.error(response.data.message || '清理失败')
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || '清理请求失败')
    }
  }

  const handleDebugDatabase = async () => {
    try {
      const response = await axios.get('/api/debug_database')
      if (response.data.success) {
        console.log('数据库调试信息:', response.data.data)
        toast.success(`数据库状态：${response.data.data.total_reports} 个年报，${response.data.data.total_companies} 家公司`)
      } else {
        toast.error('获取数据库信息失败')
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || '调试请求失败')
    }
  }

  return (
    <div className="space-y-6">
      {/* 数据导入 */}
      <div className="card">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-black dark:bg-white rounded-lg">
            <Database className="w-5 h-5 text-white dark:text-black" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">数据导入</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              导入TXT文件到本地数据库
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              TXT文件目录
            </label>
            <div className="flex space-x-3">
              <input
                type="text"
                className="input-field flex-1"
                placeholder="输入TXT文件目录路径，例如：txt"
                value={txtDir}
                onChange={(e) => setTxtDir(e.target.value)}
              />
              <motion.button
                onClick={handleImport}
                disabled={isImporting}
                className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed bg-black hover:bg-gray-800 dark:bg-white dark:hover:bg-gray-200 dark:text-black"
                whileHover={{ scale: isImporting ? 1 : 1.02 }}
                whileTap={{ scale: isImporting ? 1 : 0.98 }}
              >
                {isImporting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Upload className="w-4 h-4" />
                )}
                <span>{isImporting ? '导入中...' : '开始导入'}</span>
              </motion.button>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className="flex items-start space-x-3">
              <Info className="w-5 h-5 text-gray-500 dark:text-gray-400 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">导入说明</p>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li>• 请确保TXT文件目录存在且包含年报文本文件</li>
                  <li>• 文件名格式建议：股票代码_公司名称_报告标题.txt</li>
                  <li>• 系统会自动跳过已存在的重复文件</li>
                </ul>
              </div>
            </div>
          </div>

          {importResult && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">导入结果</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-blue-700">新增：</span>
                  <span className="font-medium text-green-600">{importResult.imported}</span>
                </div>
                <div>
                  <span className="text-blue-700">跳过：</span>
                  <span className="font-medium text-yellow-600">{importResult.skipped}</span>
                </div>
                <div>
                  <span className="text-blue-700">失败：</span>
                  <span className="font-medium text-red-600">{importResult.errors}</span>
                </div>
                <div>
                  <span className="text-blue-700">总计：</span>
                  <span className="font-medium text-blue-600">{importResult.total}</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 数据管理 */}
      <div className="card">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-black dark:bg-white rounded-lg">
            <FileText className="w-5 h-5 text-white dark:text-black" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">数据管理</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              管理和维护本地数据库
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <h3 className="font-medium text-gray-900">数据清理</h3>
            <p className="text-sm text-gray-600">
              清理数据库中的重复记录，优化存储空间
            </p>
            <motion.button
              onClick={handleCleanDuplicates}
              className="btn-secondary flex items-center space-x-2"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Trash2 className="w-4 h-4" />
              <span>清理重复数据</span>
            </motion.button>
          </div>

          <div className="space-y-3">
            <h3 className="font-medium text-gray-900">数据库状态</h3>
            <p className="text-sm text-gray-600">
              查看数据库中的年报和公司统计信息
            </p>
            <motion.button
              onClick={handleDebugDatabase}
              className="btn-secondary flex items-center space-x-2"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Bug className="w-4 h-4" />
              <span>查看数据库状态</span>
            </motion.button>
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="card">
        <div className="flex items-center space-x-3 mb-4">
          <div className="flex items-center justify-center w-8 h-8 bg-yellow-100 rounded-lg">
            <span className="text-yellow-600">💡</span>
          </div>
          <h2 className="text-xl font-semibold">使用说明</h2>
        </div>

        <div className="space-y-4 text-sm text-gray-600">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">数据导入流程</h4>
            <ol className="list-decimal list-inside space-y-1">
              <li>将年报TXT文件放入指定目录（默认为txt目录）</li>
              <li>确保文件名包含股票代码和公司信息</li>
              <li>点击"开始导入"按钮执行导入</li>
              <li>查看导入结果统计</li>
            </ol>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-2">文件格式要求</h4>
            <ul className="list-disc list-inside space-y-1">
              <li>文件格式：UTF-8编码的TXT文本文件</li>
              <li>文件名建议格式：000001_平安银行_2024年年度报告.txt</li>
              <li>文件内容：年报的完整文本内容</li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-2">注意事项</h4>
            <ul className="list-disc list-inside space-y-1">
              <li>导入过程会自动检测重复文件并跳过</li>
              <li>建议定期清理重复数据以优化性能</li>
              <li>大量文件导入可能需要较长时间，请耐心等待</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ImportData
