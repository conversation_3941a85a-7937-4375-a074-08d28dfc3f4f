import React, { useState } from 'react'
import toast from 'react-hot-toast'
import axios from 'axios'
import { motion } from 'framer-motion'
import {
  Play,
  Loader2,
  Building2,
  Hash,
  Calendar,
  Users,
  Search,
  Sparkles,
  Info
} from 'lucide-react'

interface AnalysisFormProps {
  mode: 'online' | 'local'
  onTaskStart: (taskId: string) => void
  onResultsReady?: (results: any) => void
}

const AnalysisForm: React.FC<AnalysisFormProps> = ({ mode, onTaskStart, onResultsReady }) => {
  const [formData, setFormData] = useState({
    stockCodes: '',
    keywords: '',
    searchKeyword: '年度报告',
    startDate: '2024-01-01',
    endDate: '2025-12-31',
    relatedParties: '',
  })
  const [isLoading, setIsLoading] = useState(false)
  const [focusedField, setFocusedField] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.stockCodes.trim()) {
      toast.error('请输入股票代码')
      return
    }
    
    if (!formData.keywords.trim()) {
      toast.error('请输入关键词')
      return
    }

    setIsLoading(true)
    
    try {
      const endpoint = mode === 'online' ? '/api/start_analysis' : '/api/keyword_analysis'
      const payload = {
        stock_codes: formData.stockCodes,
        keywords: formData.keywords,
        search_keyword: formData.searchKeyword,
        start_date: formData.startDate,
        end_date: formData.endDate,
        use_online: mode === 'online',
        related_parties: formData.relatedParties,
      }

      const response = await axios.post(endpoint, payload)
      
      if (response.data.success) {
        if (mode === 'online') {
          onTaskStart(response.data.data.task_id)
          toast.success('分析任务已启动')
        } else {
          toast.success('关键词分析完成')
          // 处理本地分析结果
          console.log('📊 本地分析结果:', response.data)
          if (onResultsReady && response.data.data) {
            onResultsReady(response.data.data)
          }
        }
      } else {
        toast.error(response.data.message || '分析失败')
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || '请求失败')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <motion.div
      className="card-hover"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="flex items-center space-x-4 mb-8">
        <motion.div
          className={`flex items-center justify-center w-12 h-12 rounded-xl shadow-lg ${
            mode === 'online'
              ? 'bg-gradient-to-br from-primary-500 to-blue-600'
              : 'bg-gradient-to-br from-purple-500 to-pink-600'
          }`}
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 400, damping: 17 }}
        >
          {mode === 'online' ? (
            <Search className="w-6 h-6 text-white" />
          ) : (
            <Hash className="w-6 h-6 text-white" />
          )}
        </motion.div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {mode === 'online' ? '在线年报分析' : '本地关键词分析'}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            {mode === 'online'
              ? '从巨潮资讯网下载并分析年报数据'
              : '基于本地数据库进行关键词统计分析'
            }
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* 主要输入区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 股票代码输入 */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
              <Building2 className="w-4 h-4" />
              <span>股票代码</span>
              <span className="text-error-500">*</span>
            </label>
            <div className="relative">
              <textarea
                className={`textarea-field transition-all duration-200 ${
                  focusedField === 'stockCodes' ? 'ring-2 ring-primary-500 border-transparent' : ''
                }`}
                placeholder="每行一个股票代码，例如：&#10;000001&#10;000002&#10;300454"
                value={formData.stockCodes}
                onChange={(e) => setFormData({ ...formData, stockCodes: e.target.value })}
                onFocus={() => setFocusedField('stockCodes')}
                onBlur={() => setFocusedField(null)}
                required
                rows={4}
              />
              {focusedField === 'stockCodes' && (
                <motion.div
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="absolute -bottom-6 left-0 text-xs text-gray-500 dark:text-gray-400"
                >
                  支持多个股票代码，每行一个
                </motion.div>
              )}
            </div>
          </motion.div>

          {/* 关键词输入 */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
              <Sparkles className="w-4 h-4" />
              <span>分析关键词</span>
              <span className="text-error-500">*</span>
            </label>
            <div className="relative">
              <textarea
                className={`textarea-field transition-all duration-200 ${
                  focusedField === 'keywords' ? 'ring-2 ring-primary-500 border-transparent' : ''
                }`}
                placeholder="每行一个关键词，例如：&#10;人工智能&#10;大数据&#10;云计算&#10;区块链"
                value={formData.keywords}
                onChange={(e) => setFormData({ ...formData, keywords: e.target.value })}
                onFocus={() => setFocusedField('keywords')}
                onBlur={() => setFocusedField(null)}
                required
                rows={4}
              />
              {focusedField === 'keywords' && (
                <motion.div
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="absolute -bottom-6 left-0 text-xs text-gray-500 dark:text-gray-400"
                >
                  系统将统计这些关键词在年报中的出现次数
                </motion.div>
              )}
            </div>
          </motion.div>
        </div>

        {/* 在线模式的额外配置 */}
        {mode === 'online' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800"
          >
            <div className="flex items-center space-x-2 mb-4">
              <Info className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <h3 className="font-semibold text-blue-900 dark:text-blue-100">在线搜索配置</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="flex items-center space-x-2 text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  <Search className="w-4 h-4" />
                  <span>搜索关键词</span>
                </label>
                <input
                  type="text"
                  className="input-field"
                  value={formData.searchKeyword}
                  onChange={(e) => setFormData({ ...formData, searchKeyword: e.target.value })}
                  placeholder="年度报告"
                />
              </div>

              <div>
                <label className="flex items-center space-x-2 text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  <Calendar className="w-4 h-4" />
                  <span>开始日期</span>
                </label>
                <input
                  type="date"
                  className="input-field"
                  value={formData.startDate}
                  onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                />
              </div>

              <div>
                <label className="flex items-center space-x-2 text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  <Calendar className="w-4 h-4" />
                  <span>结束日期</span>
                </label>
                <input
                  type="date"
                  className="input-field"
                  value={formData.endDate}
                  onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                />
              </div>
            </div>
          </motion.div>
        )}

        {/* 关联方输入 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
            <Users className="w-4 h-4" />
            <span>关联方分析</span>
            <span className="text-xs text-gray-500 dark:text-gray-400 font-normal">（可选）</span>
          </label>
          <div className="relative">
            <textarea
              className={`textarea-field transition-all duration-200 ${
                focusedField === 'relatedParties' ? 'ring-2 ring-primary-500 border-transparent' : ''
              }`}
              placeholder="每行一个关联方名称，例如：&#10;清华大学&#10;中科院&#10;华为技术有限公司"
              value={formData.relatedParties}
              onChange={(e) => setFormData({ ...formData, relatedParties: e.target.value })}
              onFocus={() => setFocusedField('relatedParties')}
              onBlur={() => setFocusedField(null)}
              rows={3}
            />
            {focusedField === 'relatedParties' && (
              <motion.div
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                className="absolute -bottom-6 left-0 text-xs text-gray-500 dark:text-gray-400"
              >
                分析年报中与这些关联方的合作创新情况
              </motion.div>
            )}
          </div>
        </motion.div>

        {/* 提交按钮 */}
        <motion.div
          className="flex justify-end pt-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <motion.button
            type="submit"
            disabled={isLoading}
            className="btn-primary btn-lg flex items-center space-x-3 disabled:opacity-50 disabled:cursor-not-allowed"
            whileHover={{ scale: isLoading ? 1 : 1.02 }}
            whileTap={{ scale: isLoading ? 1 : 0.98 }}
          >
            {isLoading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Play className="w-5 h-5" />
            )}
            <span className="font-semibold">
              {isLoading ? '分析中...' : '开始分析'}
            </span>
          </motion.button>
        </motion.div>
      </form>
    </motion.div>
  )
}

export default AnalysisForm
