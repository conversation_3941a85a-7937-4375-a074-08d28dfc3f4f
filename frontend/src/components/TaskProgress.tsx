import React, { useState, useEffect } from 'react'
import axios from 'axios'
import { motion } from 'framer-motion'
import {
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
  Square,
  Play,
  AlertCircle
} from 'lucide-react'

interface TaskProgressProps {
  taskId: string
  onComplete: (results: any) => void
}

const TaskProgress: React.FC<TaskProgressProps> = ({ taskId, onComplete }) => {
  const [status, setStatus] = useState<any>(null)
  const [isPolling, setIsPolling] = useState(true)

  useEffect(() => {
    if (!taskId || !isPolling) return

    const pollStatus = async () => {
      try {
        const response = await axios.get(`/api/task_status/${taskId}`)
        if (response.data.success) {
          const taskStatus = response.data.data
          setStatus(taskStatus)

          if (taskStatus.status === 'completed') {
            setIsPolling(false)
            // 获取分析结果
            try {
              const resultsResponse = await axios.get(`/api/analysis_results/${taskId}`)
              if (resultsResponse.data.success) {
                onComplete(resultsResponse.data.data)
              }
            } catch (error) {
              console.error('获取分析结果失败:', error)
            }
          } else if (taskStatus.status === 'error' || taskStatus.status === 'stopped') {
            setIsPolling(false)
          }
        }
      } catch (error) {
        console.error('获取任务状态失败:', error)
      }
    }

    // 立即执行一次
    pollStatus()

    // 设置轮询
    const interval = setInterval(pollStatus, 2000)

    return () => clearInterval(interval)
  }, [taskId, isPolling, onComplete])

  const handleStop = async () => {
    try {
      await axios.post(`/api/stop_task/${taskId}`)
      setIsPolling(false)
    } catch (error) {
      console.error('停止任务失败:', error)
    }
  }

  const getStatusIcon = () => {
    if (!status) return <Clock className="w-5 h-5 text-blue-500" />

    switch (status.status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'error':
      case 'stopped':
        return <XCircle className="w-5 h-5 text-red-500" />
      case 'running':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />
      default:
        return <Play className="w-5 h-5 text-blue-500" />
    }
  }

  const getStatusColor = () => {
    if (!status) return 'text-blue-600 dark:text-blue-400'

    switch (status.status) {
      case 'completed':
        return 'text-green-600 dark:text-green-400'
      case 'error':
      case 'stopped':
        return 'text-red-600 dark:text-red-400'
      default:
        return 'text-blue-600 dark:text-blue-400'
    }
  }

  const getStatusBgColor = () => {
    if (!status) return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'

    switch (status.status) {
      case 'completed':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
      case 'error':
      case 'stopped':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
      default:
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
    }
  }

  if (!status) {
    return (
      <motion.div
        className="card"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
      >
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
            <Clock className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white">初始化任务</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">正在获取任务状态...</p>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      className={`card border-2 ${getStatusBgColor()}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      layout
    >
      <div className="space-y-6">
        {/* 任务状态头部 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.div
              className="p-3 bg-white dark:bg-gray-800 rounded-xl shadow-sm"
              animate={{ rotate: status.status === 'running' ? 360 : 0 }}
              transition={{ duration: 2, repeat: status.status === 'running' ? Infinity : 0, ease: "linear" }}
            >
              {getStatusIcon()}
            </motion.div>
            <div>
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">任务进度</h3>
              <p className={`text-sm font-medium ${getStatusColor()}`}>
                {status.message || '正在处理...'}
              </p>
            </div>
          </div>

          {status.status === 'running' && (
            <motion.button
              onClick={handleStop}
              className="btn-danger btn-md flex items-center space-x-2"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Square className="w-4 h-4" />
              <span>停止任务</span>
            </motion.button>
          )}
        </div>

        {/* 进度条 */}
        {status.status === 'running' && (
          <motion.div
            className="space-y-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                进度: {status.current_step || 0} / {status.total_steps || 0}
              </span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {status.progress || 0}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${status.progress || 0}%` }}
                transition={{ duration: 0.5, ease: "easeOut" }}
              />
            </div>
          </motion.div>
        )}

        {/* 时间信息 */}
        {status.start_time && (
          <motion.div
            className="flex flex-wrap gap-4 text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 rounded-lg p-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className="flex items-center space-x-2">
              <Play className="w-3 h-3" />
              <span>开始: {new Date(status.start_time).toLocaleString()}</span>
            </div>
            {status.end_time && (
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-3 h-3" />
                <span>结束: {new Date(status.end_time).toLocaleString()}</span>
              </div>
            )}
            {status.status === 'running' && (
              <div className="flex items-center space-x-2">
                <Clock className="w-3 h-3" />
                <span>运行中...</span>
              </div>
            )}
          </motion.div>
        )}

        {/* 错误信息 */}
        {(status.status === 'error' || status.status === 'stopped') && (
          <motion.div
            className="flex items-start space-x-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <AlertCircle className="w-5 h-5 text-red-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-red-900 dark:text-red-100">
                {status.status === 'error' ? '任务执行失败' : '任务已停止'}
              </h4>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                {status.error_message || '请检查输入参数或联系管理员'}
              </p>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  )
}

export default TaskProgress
